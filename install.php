<?php
/**
 * Apex Company Management System
 * Installation Script
 */

// Check if already installed
if (file_exists('config/installed.lock')) {
    die('System is already installed. Delete config/installed.lock to reinstall.');
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// Handle form submissions
if ($_POST) {
    switch ($step) {
        case 2:
            // Database configuration
            $db_host = $_POST['db_host'] ?? '';
            $db_username = $_POST['db_username'] ?? '';
            $db_password = $_POST['db_password'] ?? '';
            $db_name = $_POST['db_name'] ?? '';
            
            // Test database connection
            try {
                $mysqli = new mysqli($db_host, $db_username, $db_password);
                
                if ($mysqli->connect_error) {
                    throw new Exception("Connection failed: " . $mysqli->connect_error);
                }
                
                // Create database if it doesn't exist
                $mysqli->query("CREATE DATABASE IF NOT EXISTS `$db_name`");
                $mysqli->select_db($db_name);
                
                // Update config file
                $config_content = file_get_contents('config/config.php');
                $config_content = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$db_host');", $config_content);
                $config_content = str_replace("define('DB_USERNAME', 'root');", "define('DB_USERNAME', '$db_username');", $config_content);
                $config_content = str_replace("define('DB_PASSWORD', '');", "define('DB_PASSWORD', '$db_password');", $config_content);
                $config_content = str_replace("define('DB_NAME', 'apex_company_management');", "define('DB_NAME', '$db_name');", $config_content);
                
                file_put_contents('config/config.php', $config_content);
                
                $success = 'Database connection successful!';
                // Redirect to step 3 to prevent form resubmission
                header('Location: install.php?step=3');
                exit();
                
            } catch (Exception $e) {
                $error = 'Database connection failed: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // Install database schema
            try {
                // Check if config file exists
                if (!file_exists('config/config.php')) {
                    throw new Exception('Configuration file not found. Please complete step 2 first.');
                }

                // Get database configuration from config file
                $config_content = file_get_contents('config/config.php');
                preg_match("/define\('DB_HOST', '(.+?)'\);/", $config_content, $host_match);
                preg_match("/define\('DB_USERNAME', '(.+?)'\);/", $config_content, $user_match);
                preg_match("/define\('DB_PASSWORD', '(.+?)'\);/", $config_content, $pass_match);
                preg_match("/define\('DB_NAME', '(.+?)'\);/", $config_content, $name_match);

                $db_host = $host_match[1] ?? 'localhost';
                $db_username = $user_match[1] ?? 'root';
                $db_password = $pass_match[1] ?? '';
                $db_name = $name_match[1] ?? 'apex_company_management';

                // Create database connection
                $mysqli = new mysqli($db_host, $db_username, $db_password);

                if ($mysqli->connect_error) {
                    throw new Exception("Database connection failed: " . $mysqli->connect_error);
                }

                $mysqli->set_charset("utf8");

                // Create database if it doesn't exist
                $mysqli->query("CREATE DATABASE IF NOT EXISTS `$db_name`");
                $mysqli->select_db($db_name);

                // Execute schema
                $schema = file_get_contents('database/schema.sql');
                $queries = explode(';', $schema);

                foreach ($queries as $query) {
                    $query = trim($query);
                    if (!empty($query) && !preg_match('/^(CREATE DATABASE|USE)/i', $query)) {
                        if (!$mysqli->query($query)) {
                            throw new Exception("Schema error: " . $mysqli->error . " in query: " . substr($query, 0, 100));
                        }
                    }
                }

                // Execute initial data
                $initial_data = file_get_contents('database/initial_data.sql');
                $queries = explode(';', $initial_data);

                foreach ($queries as $query) {
                    $query = trim($query);
                    if (!empty($query)) {
                        if (!$mysqli->query($query)) {
                            throw new Exception("Initial data error: " . $mysqli->error . " in query: " . substr($query, 0, 100));
                        }
                    }
                }

                $mysqli->close();

                $success = 'Database schema installed successfully!';
                // Redirect to step 4 to prevent form resubmission
                header('Location: install.php?step=4');
                exit();

            } catch (Exception $e) {
                $error = 'Database installation failed: ' . $e->getMessage();
            }
            break;
            
        case 4:
            // Create admin user
            $username = $_POST['username'] ?? '';
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            $first_name = $_POST['first_name'] ?? '';
            $last_name = $_POST['last_name'] ?? '';
            
            if ($password !== $confirm_password) {
                $error = 'Passwords do not match';
            } else {
                try {
                    require_once 'config/config.php';
                    
                    $password_hash = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Update the default admin user
                    $stmt = $mysqli->prepare("UPDATE users SET username = ?, email = ?, password_hash = ?, first_name = ?, last_name = ? WHERE role = 'super_admin' LIMIT 1");
                    $stmt->bind_param("sssss", $username, $email, $password_hash, $first_name, $last_name);
                    $stmt->execute();
                    
                    // Create installation lock file
                    file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
                    
                    $success = 'Installation completed successfully!';
                    // Redirect to step 5 to prevent form resubmission
                    header('Location: install.php?step=5');
                    exit();
                    
                } catch (Exception $e) {
                    $error = 'Admin user creation failed: ' . $e->getMessage();
                }
            }
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apex Company Management - Installation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .install-container { max-width: 600px; margin: 50px auto; }
        .install-card { border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .step-indicator { display: flex; justify-content: center; margin-bottom: 30px; }
        .step { width: 40px; height: 40px; border-radius: 50%; background: #dee2e6; color: #6c757d; display: flex; align-items: center; justify-content: center; margin: 0 10px; font-weight: bold; }
        .step.active { background: #0d6efd; color: white; }
        .step.completed { background: #198754; color: white; }
    </style>
</head>
<body>
    <div class="container install-container">
        <div class="card install-card">
            <div class="card-header bg-primary text-white text-center">
                <h3><i class="bi bi-gear"></i> Apex Company Management Installation</h3>
            </div>
            <div class="card-body p-4">
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                    <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                    <div class="step <?php echo $step >= 4 ? ($step > 4 ? 'completed' : 'active') : ''; ?>">4</div>
                    <div class="step <?php echo $step >= 5 ? 'active' : ''; ?>">5</div>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger"><i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?></div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success"><i class="bi bi-check-circle"></i> <?php echo $success; ?></div>
                <?php endif; ?>

                <?php if ($step == 1): ?>
                    <!-- Welcome Step -->
                    <h4>Welcome to Apex Company Management System</h4>
                    <p>This installation wizard will help you set up your company management system.</p>
                    
                    <h5>System Requirements:</h5>
                    <ul class="list-group mb-4">
                        <li class="list-group-item d-flex justify-content-between">
                            PHP 7.4+ 
                            <span class="badge bg-<?php echo version_compare(PHP_VERSION, '7.4.0') >= 0 ? 'success' : 'danger'; ?>">
                                <?php echo PHP_VERSION; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            MySQL 5.7+ 
                            <span class="badge bg-success">Available</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            MySQLi Extension 
                            <span class="badge bg-<?php echo extension_loaded('mysqli') ? 'success' : 'danger'; ?>">
                                <?php echo extension_loaded('mysqli') ? 'Loaded' : 'Not Loaded'; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            Write Permission 
                            <span class="badge bg-<?php echo is_writable('.') ? 'success' : 'danger'; ?>">
                                <?php echo is_writable('.') ? 'OK' : 'Not Writable'; ?>
                            </span>
                        </li>
                    </ul>
                    
                    <a href="?step=2" class="btn btn-primary btn-lg w-100">
                        <i class="bi bi-arrow-right"></i> Start Installation
                    </a>

                <?php elseif ($step == 2): ?>
                    <!-- Database Configuration -->
                    <h4>Database Configuration</h4>
                    <p>Please provide your database connection details.</p>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Database Host</label>
                            <input type="text" name="db_host" class="form-control" value="localhost" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Database Username</label>
                            <input type="text" name="db_username" class="form-control" value="root" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Database Password</label>
                            <input type="password" name="db_password" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Database Name</label>
                            <input type="text" name="db_name" class="form-control" value="apex_company_management" required>
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-database"></i> Test Connection & Continue
                        </button>
                    </form>

                <?php elseif ($step == 3): ?>
                    <!-- Database Installation -->
                    <h4>Database Installation</h4>
                    <p>Click the button below to install the database schema and initial data.</p>
                    
                    <form method="POST">
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-download"></i> Install Database
                        </button>
                    </form>

                <?php elseif ($step == 4): ?>
                    <!-- Admin User Creation -->
                    <h4>Create Admin User</h4>
                    <p>Create your administrator account to access the system.</p>
                    
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">First Name</label>
                                <input type="text" name="first_name" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Last Name</label>
                                <input type="text" name="last_name" class="form-control" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Username</label>
                            <input type="text" name="username" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Email</label>
                            <input type="email" name="email" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Password</label>
                            <input type="password" name="password" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Confirm Password</label>
                            <input type="password" name="confirm_password" class="form-control" required>
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-person-plus"></i> Create Admin & Complete Installation
                        </button>
                    </form>

                <?php elseif ($step == 5): ?>
                    <!-- Installation Complete -->
                    <div class="text-center">
                        <i class="bi bi-check-circle text-success" style="font-size: 4rem;"></i>
                        <h4 class="text-success mt-3">Installation Complete!</h4>
                        <p>Your Apex Company Management System has been successfully installed.</p>
                        
                        <div class="alert alert-warning">
                            <strong>Important:</strong> For security reasons, please delete the <code>install.php</code> file from your server.
                        </div>
                        
                        <a href="auth/login.php" class="btn btn-success btn-lg">
                            <i class="bi bi-box-arrow-in-right"></i> Login to System
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
