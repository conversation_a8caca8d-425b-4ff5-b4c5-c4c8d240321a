<?php
/**
 * Apex Company Management System
 * Settings
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('manage_settings');

$page_title = 'Settings';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-gear"></i>
                    System Settings
                </h1>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-gear" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">System Settings</h4>
                        <p>This module is under development.</p>
                        <p>You can configure company settings, email templates, and system preferences here.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
