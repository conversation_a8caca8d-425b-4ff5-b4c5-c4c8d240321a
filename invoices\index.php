<?php
/**
 * Apex Company Management System
 * Invoices List
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('view_invoices');

$page_title = 'Invoices';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-receipt"></i>
                    Invoices
                </h1>
                <?php if (has_permission('create_invoices')): ?>
                <a href="add.php" class="btn btn-primary">
                    <i class="bi bi-plus"></i>
                    Create Invoice
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-receipt" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">Invoice Management</h4>
                        <p>This module is under development.</p>
                        <p>You can create and manage invoices with optional GST calculations here.</p>
                        <?php if (has_permission('create_invoices')): ?>
                        <a href="add.php" class="btn btn-primary mt-3">
                            <i class="bi bi-plus"></i>
                            Create First Invoice
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
